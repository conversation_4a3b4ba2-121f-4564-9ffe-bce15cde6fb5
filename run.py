import asyncio
import uvicorn
import os
import sys
import psutil
import logging
from typing import List, Optional, Any

# Import Self-Healer components (optional integration)
SELF_HEALER_AVAILABLE = False
SelfHealerManager: Optional[Any] = None
run_dashboard: Optional[Any] = None

def load_self_healer():
    """Dynamically load Self-Healer components if available."""
    global SELF_HEALER_AVAILABLE, SelfHealerManager, run_dashboard

    try:
        # Check if Self-Healer directory exists
        self_healer_path = os.path.join(os.path.dirname(__file__), 'Self-Healer')
        if not os.path.exists(self_healer_path):
            print("Self-Healer directory not found - running without auto-healing capabilities")
            return False

        # Add Self-Healer to path
        if self_healer_path not in sys.path:
            sys.path.insert(0, self_healer_path)

        # Try to import the modules directly
        # pylint: disable=import-error
        from core.healer_manager import SelfHealerManager as _SelfHealerManager  # type: ignore
        from dashboard.dashboard import run_dashboard as _run_dashboard  # type: ignore

        # Assign to global variables
        SelfHealerManager = _SelfHealerManager
        run_dashboard = _run_dashboard

        SELF_HEALER_AVAILABLE = True
        return True

    except ImportError as e:
        print(f"Self-Healer modules not available: {e}")
        return False
    except Exception as e:
        print(f"Error loading Self-Healer: {e}")
        return False

# Try to load Self-Healer components
load_self_healer()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def kill_processes_on_ports(ports: List[int]) -> None:
    """Kill any processes running on the specified ports."""
    for port in ports:
        try:
            for proc in psutil.process_iter(['pid', 'name', 'connections']):
                try:
                    # Skip system processes (PID 0, 4, etc.)
                    if proc.info['pid'] in [0, 4]:
                        continue
                    
                    # Check if process has connections (using net_connections instead of deprecated connections)
                    try:
                        connections = proc.net_connections()
                    except (psutil.AccessDenied, psutil.NoSuchProcess, AttributeError):
                        # Some processes don't have net_connections method or we don't have access
                        connections = []

                    for conn in connections:
                        if hasattr(conn, 'laddr') and conn.laddr and conn.laddr.port == port:
                            logger.info(f"Killing process {proc.info['pid']} ({proc.info['name']}) using port {port}")
                            try:
                                proc.terminate()

                                # Wait for process to terminate
                                proc.wait(timeout=3)
                                logger.info(f"Successfully terminated process {proc.info['pid']}")
                            except (psutil.TimeoutExpired, psutil.AccessDenied) as e:
                                logger.warning(f"Could not terminate process {proc.info['pid']}: {e}")
                            except Exception as e:
                                logger.warning(f"Error terminating process {proc.info['pid']}: {e}")
                            
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    # Process no longer exists or we don't have permission
                    continue
                except Exception as e:
                    logger.warning(f"Error checking process connections: {e}")
                    continue
                    
        except Exception as e:
            logger.warning(f"Error scanning processes for port {port}: {e}")
            continue

def check_port_available(port: int) -> bool:
    """Check if a port is available."""
    import socket
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('127.0.0.1', port))
            return True
    except OSError:
        return False

async def _configure_self_healer_monitoring(healer_manager, app_port):
    """Configure Self-Healer to monitor the N8N Builder application."""
    try:
        # Configure Self-Healer to monitor the application endpoint
        app_url = f"http://localhost:{app_port}"

        # Add application monitoring configuration
        monitoring_config = {
            'app_url': app_url,
            'health_endpoint': f"{app_url}/llm/health",
            'monitoring_interval': 30,  # Check every 30 seconds
            'error_patterns': [
                'HTTP/1.1 500',
                'HTTP/1.1 404',
                'Internal Server Error',
                'Connection refused',
                'Timeout'
            ]
        }

        # Pass configuration to Self-Healer (it will pull/monitor on its own)
        await healer_manager.configure_application_monitoring(monitoring_config)

        logger.info(f"Self-Healer configured to monitor N8N Builder at {app_url}")
        logger.info("Self-Healer will actively monitor application health and logs")

    except Exception as e:
        logger.warning(f"Could not configure Self-Healer application monitoring: {e}")
        logger.info("Self-Healer will continue with log file monitoring only")

async def main():
    # Kill any existing processes on our ports (including dashboard port)
    ports_to_clear = [8002, 8080]
    if SELF_HEALER_AVAILABLE:
        ports_to_clear.append(8081)  # Dashboard port

    # Check if ports are available first
    for port in ports_to_clear:
        if not check_port_available(port):
            logger.info(f"Port {port} is in use, attempting to free it...")
            kill_processes_on_ports([port])

            # Wait a moment and check again
            await asyncio.sleep(1)
            if not check_port_available(port):
                logger.warning(f"Port {port} is still in use after cleanup attempt")

    # Initialize Self-Healer if available
    healer_manager = None
    dashboard_task = None

    if SELF_HEALER_AVAILABLE and SelfHealerManager is not None and run_dashboard is not None:
        try:
            logger.info("Initializing Self-Healer system...")
            healer_manager = SelfHealerManager()
            await healer_manager.start()
            logger.info("Self-Healer system started successfully")

            # Configure Self-Healer to monitor the N8N Builder application
            await _configure_self_healer_monitoring(healer_manager, port)

            # Start dashboard
            logger.info("Starting Self-Healer dashboard on port 8081...")
            dashboard_task = asyncio.create_task(run_dashboard(healer_manager, port=8081))
            logger.info("Self-Healer dashboard started at http://localhost:8081")

        except Exception as e:
            logger.error(f"Failed to start Self-Healer: {e}")
            logger.info("Continuing without Self-Healer...")

    try:
        # Find an available port starting from 8002
        port = 8002
        max_attempts = 10
        for attempt in range(max_attempts):
            if check_port_available(port):
                break
            port += 1
            if attempt == max_attempts - 1:
                logger.error(f"Could not find an available port after {max_attempts} attempts")
                return

        logger.info(f"Using port {port} for N8N Builder server")

        # Start the FastAPI server
        config = uvicorn.Config(
            "n8n_builder.app:app",
            host="127.0.0.1",
            port=port,
            log_level="info",
            reload=False  # Disable reload to avoid port conflicts
        )
        server = uvicorn.Server(config)

        logger.info(f"Starting N8N Builder server on port {port}...")
        if SELF_HEALER_AVAILABLE and healer_manager:
            logger.info("Self-Healer is active - automatic error healing enabled")
            logger.info("Dashboard available at: http://localhost:8081")

        logger.info(f"N8N Builder will be available at: http://localhost:{port}")

        await server.serve()

    finally:
        # Cleanup Self-Healer on shutdown
        if healer_manager:
            logger.info("Shutting down Self-Healer system...")
            await healer_manager.stop()

        if dashboard_task:
            dashboard_task.cancel()
            try:
                await dashboard_task
            except asyncio.CancelledError:
                pass

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Shutting down server...")
    except Exception as e:
        logger.error(f"Error running server: {e}")
        sys.exit(1) 